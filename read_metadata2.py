import numpy as np
import xml.etree.ElementTree as ET
import matplotlib.pyplot as plt
import open3d as o3d
from mpl_toolkits.mplot3d import Axes3D

def read_metadata(xml_file):
    tree = ET.parse(xml_file)
    root = tree.getroot()

    width = int(root.find(".//genistreamtraits/parameter[@name='width']").text)
    height = int(root.find(".//genistreamtraits/parameter[@name='height']").text)

    a_scale = float(root.find(".//genistreamtraits/parameter[@name='a axis range scale']").text)
    a_offset = float(root.find(".//genistreamtraits/parameter[@name='a axis range offset']").text)

    b_scale = float(root.find(".//genistreamtraits/parameter[@name='b axis range scale']").text)
    b_offset = float(root.find(".//genistreamtraits/parameter[@name='b axis range offset']").text)

    c_scale = float(root.find(".//genistreamtraits/parameter[@name='c axis range scale']").text)
    c_offset = float(root.find(".//genistreamtraits/parameter[@name='c axis range offset']").text)

    dtype = "uint16"  # Coord3D_C16

    return width, height, dtype, a_scale, a_offset, b_scale, b_offset, c_scale, c_offset

def read_range_image(dat_file, width, height, dtype):
    dtype_map = {
        "uint8": np.uint8,
        "uint16": np.uint16,
        "int32": np.int32,
        "float32": np.float32
    }
    data = np.fromfile(dat_file, dtype=dtype_map[dtype])
    print(f"Loaded {data.size} values from {dat_file}")

    expected_size = width * height
    if data.size < expected_size:
        raise ValueError(f"Not enough data: expected {expected_size}, got {data.size}")
    elif data.size > expected_size:
        print(f"Warning: file larger than expected, truncating to {expected_size}")
        data = data[:expected_size]

    img = data.reshape((height, width))
    return img

def range_to_pointcloud(range_img, a_scale, a_offset, b_scale, b_offset, c_scale, c_offset):
    h, w = range_img.shape

    xs = np.arange(w)
    ys = np.arange(h)
    grid_x, grid_y = np.meshgrid(xs, ys)

    X = grid_x * a_scale + a_offset
    Y = grid_y * b_scale + b_offset
    Z = range_img * c_scale + c_offset

    points = np.vstack((X.flatten(), Y.flatten(), Z.flatten())).T

    # Lọc điểm rỗng
    mask = (Z.flatten() > 0) & np.isfinite(Z.flatten())
    points = points[mask]

    return points

if __name__ == "__main__":
    xml_file = r"D:\SICK\3DSick\anhtraning\anh1\anh1.xml"
    dat_file = r"D:\SICK\3DSick\anhtraning\anh1\anh1.dat"

    width, height, dtype, a_scale, a_offset, b_scale, b_offset, c_scale, c_offset = read_metadata(xml_file)
    print(f"Image size: {width}x{height}, dtype={dtype}")

    range_img = read_range_image(dat_file, width, height, dtype)

    # Vẽ range image 2D
    plt.imshow(range_img, cmap="jet")
    plt.colorbar(label="Depth (raw units)")
    plt.title("3D Range Image")
    plt.show()

    points = range_to_pointcloud(range_img, a_scale, a_offset, b_scale, b_offset, c_scale, c_offset)

    # ---- Lưu point cloud ra file PLY ----
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    o3d.io.write_point_cloud("output.ply", pcd)
    print("✅ Saved point cloud to output.ply")

    # ---- Preview bằng matplotlib 3D ----
    z_vals = points[:, 2]
    z_norm = (z_vals - z_vals.min()) / (z_vals.max() - z_vals.min() + 1e-9)

    fig = plt.figure(figsize=(8, 6))
    ax = fig.add_subplot(111, projection='3d')
    ax.scatter(points[::50, 0], points[::50, 1], points[::50, 2],
               c=z_norm[::50], cmap="jet", s=1)
    ax.set_xlabel("X (mm)")
    ax.set_ylabel("Y (mm)")
    ax.set_zlabel("Z (mm)")
    plt.title("Point Cloud Preview (subsampled)")
    plt.show()
