import numpy as np
import matplotlib.pyplot as plt
import cv2
import struct
import xml.etree.ElementTree as ET
import os
from matplotlib.colors import LinearSegmentedColormap
import open3d as o3d

class SICK3DDataReader:
    def __init__(self, dat_file, xml_file):
        self.dat_file = dat_file
        self.xml_file = xml_file
        self.metadata = self.parse_xml()
        
    def parse_xml(self):
        """Parse XML metadata file"""
        tree = ET.parse(self.xml_file)
        root = tree.getroot()
        
        metadata = {}
        
        # Extract key parameters
        for param in root.findall('.//parameter'):
            name = param.get('name')
            if name:
                metadata[name] = param.text
                
        # Extract genistreamtraits parameters
        for param in root.findall('.//genistreamtraits/parameter'):
            name = param.get('name')
            if name:
                metadata[name] = param.text
                
        return metadata
    
    def read_binary_data(self):
        """Read binary data from .dat file"""
        with open(self.dat_file, 'rb') as f:
            data = f.read()

        print(f"Total file size: {len(data)} bytes")

        # Parse XML to get component information
        tree = ET.parse(self.xml_file)
        root = tree.getroot()

        # Get delivered frame height from XML
        delivered_height = int(root.find('.//parameter[@name="delivered frame height"]').text)
        print(f"Delivered frame height: {delivered_height}")

        # Find Range component
        range_component = root.find('.//subcomponent[@name="Range"]')
        range_size = int(range_component.find('parameter[@name="size"]').text)
        range_width = int(range_component.find('parameter[@name="width"]').text)

        # Find Intensity component
        intensity_component = root.find('.//subcomponent[@name="Intensity"]')
        intensity_size = int(intensity_component.find('parameter[@name="size"]').text)
        intensity_width = int(intensity_component.find('parameter[@name="width"]').text)

        print(f"Range: size={range_size}, width={range_width}")
        print(f"Intensity: size={intensity_size}, width={intensity_width}")

        # Calculate actual dimensions for the delivered frame
        # The file contains multiple scan lines (delivered_height lines)
        actual_range_size = range_width * delivered_height * 2  # 2 bytes per WORD
        actual_intensity_size = intensity_width * delivered_height  # 1 byte per BYTE

        print(f"Expected data sizes: Range={actual_range_size}, Intensity={actual_intensity_size}")

        # Try to find the actual data start position
        # Sometimes there's a header or the data is structured differently
        data_start_offset = 0

        # Try different offsets to find non-zero data
        for offset in [0, 100, 1000, 10000, 50000]:
            if offset + actual_range_size <= len(data):
                test_range = struct.unpack(f'<{min(100, (actual_range_size - offset)//2)}H',
                                         data[offset:offset + min(200, actual_range_size - offset)])
                non_zero_count = sum(1 for x in test_range if x > 0)
                print(f"Offset {offset}: {non_zero_count} non-zero values out of {len(test_range)}")
                if non_zero_count > 10:  # Found some data
                    data_start_offset = offset
                    break

        print(f"Using data start offset: {data_start_offset}")

        # Read Range data (WORD = 2 bytes per pixel, little endian)
        range_start = data_start_offset
        range_end = range_start + actual_range_size
        if range_end <= len(data):
            range_data = struct.unpack(f'<{actual_range_size//2}H', data[range_start:range_end])
            range_array = np.array(range_data).reshape(delivered_height, range_width)
        else:
            print("Not enough data for full range array, using available data")
            available_range = (len(data) - range_start) // 2
            range_data = struct.unpack(f'<{available_range}H', data[range_start:range_start + available_range*2])
            # Pad or truncate to fit expected dimensions
            if len(range_data) < delivered_height * range_width:
                range_data = list(range_data) + [0] * (delivered_height * range_width - len(range_data))
            range_array = np.array(range_data[:delivered_height * range_width]).reshape(delivered_height, range_width)

        # Read Intensity data (BYTE = 1 byte per pixel)
        intensity_start = range_end
        intensity_end = intensity_start + actual_intensity_size
        if intensity_end <= len(data):
            intensity_data = struct.unpack(f'<{actual_intensity_size}B', data[intensity_start:intensity_end])
            intensity_array = np.array(intensity_data).reshape(delivered_height, intensity_width)
        else:
            print("Not enough data for full intensity array, using available data")
            available_intensity = len(data) - intensity_start
            if available_intensity > 0:
                intensity_data = struct.unpack(f'<{available_intensity}B', data[intensity_start:intensity_start + available_intensity])
                # Pad or truncate to fit expected dimensions
                if len(intensity_data) < delivered_height * intensity_width:
                    intensity_data = list(intensity_data) + [0] * (delivered_height * intensity_width - len(intensity_data))
                intensity_array = np.array(intensity_data[:delivered_height * intensity_width]).reshape(delivered_height, intensity_width)
            else:
                intensity_array = np.zeros((delivered_height, intensity_width), dtype=np.uint8)

        print(f"Final shapes - Range: {range_array.shape}, Intensity: {intensity_array.shape}")
        print(f"Range data stats: min={range_array.min()}, max={range_array.max()}, mean={range_array.mean():.2f}")
        print(f"Intensity data stats: min={intensity_array.min()}, max={intensity_array.max()}, mean={intensity_array.mean():.2f}")
        print(f"Non-zero range values: {np.sum(range_array > 0)}")

        return range_array, intensity_array
    
    def convert_to_world_coordinates(self, range_array):
        """Convert range data to world coordinates"""
        # Get calibration parameters from metadata
        c_scale = float(self.metadata.get('c axis range scale', 0.00034326629247516394))
        c_offset = float(self.metadata.get('c axis range offset', 25.033672332763672))
        a_scale = float(self.metadata.get('a axis range scale', 0.015864396467804909))
        a_offset = float(self.metadata.get('a axis range offset', 24.03056526184082))

        # Convert range values to Z coordinates (depth)
        z_coords = range_array.astype(np.float64) * c_scale + c_offset

        # Create X coordinates based on pixel position
        height, width = range_array.shape
        x_indices = np.arange(width)
        x_coords = x_indices * a_scale + a_offset

        # Create meshgrid for coordinates
        X, Y = np.meshgrid(x_coords, np.arange(height))

        # Filter out invalid points (range = 0 or missing value)
        missing_value = int(self.metadata.get('c axis range missing value', 0))
        valid_mask = (range_array > missing_value) & (range_array < 65535)

        print(f"Valid points: {np.sum(valid_mask)} out of {range_array.size}")

        return X, Y, z_coords, valid_mask

def visualize_3d_data(dat_file, xml_file, save_images=True):
    """Main function to visualize 3D data"""
    reader = SICK3DDataReader(dat_file, xml_file)
    
    # Read data
    range_array, intensity_array = reader.read_binary_data()
    X, Y, Z, valid_mask = reader.convert_to_world_coordinates(range_array)
    
    # Create figure with subplots
    fig = plt.figure(figsize=(20, 15))
    
    # 1. Range Image (Depth Map)
    plt.subplot(2, 3, 1)
    range_display = np.where(valid_mask, range_array, np.nan)
    plt.imshow(range_display, cmap='jet', aspect='auto')
    plt.colorbar(label='Range (raw values)')
    plt.title('Range Image (Depth Map)')
    plt.xlabel('X pixels')
    plt.ylabel('Y pixels')
    
    # 2. Intensity Image
    plt.subplot(2, 3, 2)
    plt.imshow(intensity_array, cmap='gray', aspect='auto')
    plt.colorbar(label='Intensity')
    plt.title('Intensity Image')
    plt.xlabel('X pixels')
    plt.ylabel('Y pixels')
    
    # 3. World Coordinates Z (Height Map)
    plt.subplot(2, 3, 3)
    z_display = np.where(valid_mask, Z, np.nan)
    plt.imshow(z_display, cmap='terrain', aspect='auto')
    plt.colorbar(label='Z coordinate (mm)')
    plt.title('Height Map (World Z coordinates)')
    plt.xlabel('X pixels')
    plt.ylabel('Y pixels')
    
    # 4. 3D Surface Plot
    ax = fig.add_subplot(2, 3, 4, projection='3d')
    
    # Downsample for better performance
    step = 10
    X_sub = X[::step, ::step]
    Y_sub = Y[::step, ::step]
    Z_sub = Z[::step, ::step]
    valid_sub = valid_mask[::step, ::step]
    intensity_sub = intensity_array[::step, ::step]
    
    # Only plot valid points
    X_valid = X_sub[valid_sub]
    Y_valid = Y_sub[valid_sub]
    Z_valid = Z_sub[valid_sub]
    intensity_valid = intensity_sub[valid_sub]
    
    # Create 3D scatter plot with intensity coloring
    scatter = ax.scatter(X_valid, Y_valid, Z_valid, 
                        c=intensity_valid, cmap='viridis', 
                        s=1, alpha=0.6)
    ax.set_xlabel('X (mm)')
    ax.set_ylabel('Y (pixels)')
    ax.set_zlabel('Z (mm)')
    ax.set_title('3D Point Cloud (colored by intensity)')
    plt.colorbar(scatter, ax=ax, label='Intensity')
    
    # 5. Cross-section view
    plt.subplot(2, 3, 5)
    # Take middle row cross-section
    mid_row = range_array.shape[0] // 2
    x_profile = X[mid_row, :]
    z_profile = Z[mid_row, :]
    valid_profile = valid_mask[mid_row, :]
    
    plt.plot(x_profile[valid_profile], z_profile[valid_profile], 'b-', linewidth=2)
    plt.xlabel('X coordinate (mm)')
    plt.ylabel('Z coordinate (mm)')
    plt.title(f'Cross-section at row {mid_row}')
    plt.grid(True)
    
    # 6. Enhanced 2D visualization with intensity overlay
    plt.subplot(2, 3, 6)
    # Create RGB image combining range and intensity
    range_norm = cv2.normalize(range_display.astype(np.float32), None, 0, 255, cv2.NORM_MINMAX)
    intensity_norm = cv2.normalize(intensity_array.astype(np.float32), None, 0, 255, cv2.NORM_MINMAX)
    
    # Create color image
    rgb_image = np.zeros((range_array.shape[0], range_array.shape[1], 3), dtype=np.uint8)
    rgb_image[:, :, 0] = intensity_norm  # Red channel = intensity
    rgb_image[:, :, 1] = range_norm      # Green channel = range
    rgb_image[:, :, 2] = range_norm * 0.5 + intensity_norm * 0.5  # Blue = combination
    
    plt.imshow(rgb_image, aspect='auto')
    plt.title('Enhanced 2D View (R=Intensity, G=Range, B=Combined)')
    plt.xlabel('X pixels')
    plt.ylabel('Y pixels')
    
    plt.tight_layout()
    
    if save_images:
        # Save the complete figure
        base_name = os.path.splitext(os.path.basename(dat_file))[0]
        plt.savefig(f'{base_name}_complete_analysis.png', dpi=300, bbox_inches='tight')
        
        # Save individual high-quality images
        
        # Enhanced 2D image
        plt.figure(figsize=(12, 8))
        plt.imshow(rgb_image, aspect='auto')
        plt.title('Enhanced 3D Visualization', fontsize=16)
        plt.xlabel('X pixels', fontsize=14)
        plt.ylabel('Y pixels', fontsize=14)
        plt.colorbar(label='Combined Range & Intensity')
        plt.savefig(f'{base_name}_enhanced_2d.png', dpi=300, bbox_inches='tight')
        
        # High-quality range image
        plt.figure(figsize=(12, 8))
        plt.imshow(range_display, cmap='jet', aspect='auto')
        plt.colorbar(label='Range (raw values)')
        plt.title('High-Quality Range Image', fontsize=16)
        plt.xlabel('X pixels', fontsize=14)
        plt.ylabel('Y pixels', fontsize=14)
        plt.savefig(f'{base_name}_range_hq.png', dpi=300, bbox_inches='tight')
        
        print(f"Images saved:")
        print(f"- {base_name}_complete_analysis.png")
        print(f"- {base_name}_enhanced_2d.png") 
        print(f"- {base_name}_range_hq.png")
    
    plt.show()
    
    return range_array, intensity_array, X, Y, Z, valid_mask

def create_point_cloud_from_sick_data(dat_file, xml_file, save_ply=True):
    """Create Open3D point cloud from SICK data"""
    reader = SICK3DDataReader(dat_file, xml_file)
    range_array, intensity_array = reader.read_binary_data()
    X, Y, Z, valid_mask = reader.convert_to_world_coordinates(range_array)

    # Extract valid points
    valid_points = valid_mask.flatten()

    if np.sum(valid_points) == 0:
        print("No valid points found in the data!")
        return None

    points_3d = np.column_stack([
        X.flatten()[valid_points],
        Y.flatten()[valid_points],
        Z.flatten()[valid_points]
    ])

    # Create Open3D point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points_3d)

    # Add intensity as colors
    intensities = intensity_array.flatten()[valid_points]
    if len(intensities) > 0 and intensities.max() > 0:
        colors = plt.cm.viridis(intensities / intensities.max())[:, :3]
        pcd.colors = o3d.utility.Vector3dVector(colors)
    else:
        # Default color if no intensity data
        colors = np.tile([0.5, 0.5, 0.5], (len(points_3d), 1))
        pcd.colors = o3d.utility.Vector3dVector(colors)

    if save_ply:
        base_name = os.path.splitext(os.path.basename(dat_file))[0]
        o3d.io.write_point_cloud(f"{base_name}_from_dat.ply", pcd)
        print(f"Point cloud saved as {base_name}_from_dat.ply")

    return pcd

if __name__ == "__main__":
    # Example usage
    dat_file = "anhtraning/anh1/anh1.dat"
    xml_file = "anhtraning/anh1/anh1.xml"

    print(f"Checking files:")
    print(f"DAT file: {dat_file} - exists: {os.path.exists(dat_file)}")
    print(f"XML file: {xml_file} - exists: {os.path.exists(xml_file)}")

    if os.path.exists(dat_file) and os.path.exists(xml_file):
        print("Analyzing SICK 3D data...")

        try:
            # Visualize data
            range_data, intensity_data, X, Y, Z, valid_mask = visualize_3d_data(dat_file, xml_file)

            # Create point cloud
            pcd = create_point_cloud_from_sick_data(dat_file, xml_file)

            print("Analysis completed successfully!")
            print("3D visualization and 2D images have been generated.")

        except Exception as e:
            print(f"Error during analysis: {e}")
            import traceback
            traceback.print_exc()

    else:
        print(f"Files not found!")
        print("Available files in anhtraning/anh1/:")
        if os.path.exists("anhtraning/anh1/"):
            for f in os.listdir("anhtraning/anh1/"):
                print(f"  {f}")
        else:
            print("Directory anhtraning/anh1/ does not exist")
