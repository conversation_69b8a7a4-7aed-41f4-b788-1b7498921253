#!/usr/bin/env python3
"""
Batch process all SICK 3D data files in a directory
Creates beautiful 2D images from all .dat files found
"""

import os
import glob
from visualize_3d_data import visualize_3d_data, create_point_cloud_from_sick_data

def find_dat_xml_pairs(directory):
    """Find all .dat files with corresponding .xml files"""
    dat_files = glob.glob(os.path.join(directory, "**/*.dat"), recursive=True)
    pairs = []
    
    for dat_file in dat_files:
        xml_file = dat_file.replace('.dat', '.xml')
        if os.path.exists(xml_file):
            pairs.append((dat_file, xml_file))
        else:
            print(f"Warning: No XML file found for {dat_file}")
    
    return pairs

def process_all_files(directory="anhtraning", create_point_clouds=False):
    """Process all SICK data files in directory"""
    print(f"🔍 Searching for SICK data files in: {directory}")
    
    pairs = find_dat_xml_pairs(directory)
    
    if not pairs:
        print(f"❌ No .dat/.xml pairs found in {directory}")
        return
    
    print(f"📁 Found {len(pairs)} file pairs to process:")
    for dat, xml in pairs:
        print(f"   • {os.path.basename(dat)}")
    
    print("\n🚀 Starting batch processing...")
    
    successful = 0
    failed = 0
    
    for i, (dat_file, xml_file) in enumerate(pairs, 1):
        base_name = os.path.splitext(os.path.basename(dat_file))[0]
        print(f"\n[{i}/{len(pairs)}] Processing: {base_name}")
        
        try:
            # Create 2D visualizations
            print("  📊 Creating 2D visualizations...")
            range_data, intensity_data, X, Y, Z, valid_mask = visualize_3d_data(
                dat_file, xml_file, save_images=True
            )
            
            # Optionally create point clouds
            if create_point_clouds:
                print("  🔵 Creating 3D point cloud...")
                pcd = create_point_cloud_from_sick_data(dat_file, xml_file, save_ply=True)
            
            print(f"  ✅ Success! Valid points: {valid_mask.sum():,}")
            successful += 1
            
        except Exception as e:
            print(f"  ❌ Failed: {e}")
            failed += 1
    
    print(f"\n🎉 Batch processing completed!")
    print(f"   ✅ Successful: {successful}")
    print(f"   ❌ Failed: {failed}")
    print(f"   📁 Check current directory for generated images")

def main():
    import sys
    
    # Default settings
    directory = "anhtraning"
    create_point_clouds = False
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        directory = sys.argv[1]
    
    if len(sys.argv) > 2 and sys.argv[2].lower() in ['true', '1', 'yes', 'y']:
        create_point_clouds = True
    
    print("🔧 SICK 3D Data Batch Processor")
    print("=" * 40)
    print(f"Directory: {directory}")
    print(f"Create point clouds: {create_point_clouds}")
    print("=" * 40)
    
    if not os.path.exists(directory):
        print(f"❌ Directory not found: {directory}")
        return
    
    process_all_files(directory, create_point_clouds)

if __name__ == "__main__":
    main()
