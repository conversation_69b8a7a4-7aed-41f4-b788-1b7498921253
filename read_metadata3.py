import numpy as np
import xml.etree.ElementTree as ET
import matplotlib.pyplot as plt
import open3d as o3d
from sklearn.cluster import DBSCAN
import os

def read_metadata(xml_file):
    tree = ET.parse(xml_file)
    root = tree.getroot()

    width = int(root.find(".//genistreamtraits/parameter[@name='width']").text)
    height = int(root.find(".//genistreamtraits/parameter[@name='height']").text)

    # Scale + offset từ XML
    ax_scale = float(root.find(".//genistreamtraits/parameter[@name='a axis range scale']").text)
    ax_offset = float(root.find(".//genistreamtraits/parameter[@name='a axis range offset']").text)

    bx_scale = float(root.find(".//genistreamtraits/parameter[@name='b axis range scale']").text)
    bx_offset = float(root.find(".//genistreamtraits/parameter[@name='b axis range offset']").text)

    cz_scale = float(root.find(".//genistreamtraits/parameter[@name='c axis range scale']").text)
    cz_offset = float(root.find(".//genistreamtraits/parameter[@name='c axis range offset']").text)

    dtype = "uint16"  # thường là Coord3D_C16

    return width, height, dtype, ax_scale, ax_offset, bx_scale, bx_offset, cz_scale, cz_offset

def read_range_image(dat_file, width, height, dtype):
    dtype_map = {
        "uint8": np.uint8,
        "uint16": np.uint16,
        "int32": np.int32,
        "float32": np.float32
    }
    data = np.fromfile(dat_file, dtype=dtype_map[dtype])
    expected_size = width * height
    if data.size > expected_size:
        data = data[:expected_size]
    img = data.reshape((height, width))
    return img

def range_to_pointcloud(range_img, ax_scale, ax_offset, bx_scale, bx_offset, cz_scale, cz_offset):
    h, w = range_img.shape
    xs = np.arange(w)
    ys = np.arange(h)
    xx, yy = np.meshgrid(xs, ys)

    # chuyển pixel (u,v,depth) sang (X,Y,Z) thực
    X = xx * ax_scale + ax_offset
    Y = yy * bx_scale + bx_offset
    Z = range_img.astype(np.float32) * cz_scale + cz_offset

    points = np.stack((X, Y, Z), axis=-1).reshape(-1, 3)
    return points

def detect_objects(points, eps=0.05, min_samples=30):
    """
    Phát hiện vật thể bằng clustering (DBSCAN).
    """
    clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(points)
    return clustering.labels_

if __name__ == "__main__":
    folder_path = input("Enter the folder path containing .xml and .dat files: ").strip()

    # Kiểm tra sự tồn tại của thư mục
    if not os.path.exists(folder_path):
        print(f"Error: Folder '{folder_path}' does not exist!")  # Thêm thông báo lỗi
        exit(1)

    files = os.listdir(folder_path)

    # Lọc các file .xml và .dat
    xml_files = sorted([f for f in files if f.endswith(".xml")])
    dat_files = sorted([f for f in files if f.endswith(".dat")])

    # Đảm bảo số lượng file .xml và .dat khớp nhau
    if len(xml_files) != len(dat_files):
        print("Error: Number of .xml and .dat files do not match!")
        exit(1)

    for xml_file, dat_file in zip(xml_files, dat_files):
        xml_path = os.path.join(folder_path, xml_file)
        dat_path = os.path.join(folder_path, dat_file)

        print(f"Processing: {xml_file} and {dat_file}")

        # 1. Đọc metadata
        width, height, dtype, ax_scale, ax_offset, bx_scale, bx_offset, cz_scale, cz_offset = read_metadata(xml_path)
        print(f"Image size: {width}x{height}, dtype={dtype}")

        # 2. Đọc ảnh range
        range_img = read_range_image(dat_path, width, height, dtype)

        # 3. Chuyển sang point cloud
        points = range_to_pointcloud(range_img, ax_scale, ax_offset, bx_scale, bx_offset, cz_scale, cz_offset)
        print(f"Point cloud shape: {points.shape}")

        # 4. Hiển thị range image
        plt.figure(figsize=(10, 4))
        plt.imshow(range_img, cmap="jet")
        plt.title(f"Range Image (Depth map) - {xml_file}")
        plt.colorbar(label="Depth value")
        plt.show()

        # 5. Tạo Open3D point cloud
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)

        # Màu theo range image (giống ảnh gốc)
        z_norm = (range_img.astype(np.float32) - range_img.min()) / (np.ptp(range_img) + 1e-9)
        colors = plt.cm.jet(z_norm.flatten())[:, :3]  # Flatten để khớp với point cloud
        pcd.colors = o3d.utility.Vector3dVector(colors)

        o3d.visualization.draw_geometries([pcd], window_name=f"Point Cloud - {xml_file}")

        # 6. Phát hiện vật thể (clustering)
        labels = detect_objects(points)
        n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
        print(f"Number of clusters (objects): {n_clusters}")

        if n_clusters > 0:
            norm_labels = (labels - labels.min()) / (labels.max() - labels.min() + 1e-9)
            cluster_colors = plt.cm.tab20(norm_labels)[:, :3]
            cluster_colors[labels == -1] = [0, 0, 0]  # noise = đen
            pcd.colors = o3d.utility.Vector3dVector(cluster_colors)
            o3d.visualization.draw_geometries([pcd], window_name=f"Point Cloud - clustered - {xml_file}")
