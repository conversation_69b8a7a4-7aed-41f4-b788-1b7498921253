#!/usr/bin/env python3
"""
Compare the original PointCloud.ply with the generated point clouds from .dat files
Creates side-by-side visualizations and analysis
"""

import open3d as o3d
import numpy as np
import matplotlib.pyplot as plt
import os

def load_and_analyze_pointcloud(ply_file):
    """Load point cloud and return basic statistics"""
    if not os.path.exists(ply_file):
        print(f"File not found: {ply_file}")
        return None
    
    pcd = o3d.io.read_point_cloud(ply_file)
    points = np.asarray(pcd.points)
    
    if len(points) == 0:
        print(f"No points found in {ply_file}")
        return None
    
    stats = {
        'file': ply_file,
        'num_points': len(points),
        'bounds': {
            'x_min': points[:, 0].min(),
            'x_max': points[:, 0].max(),
            'y_min': points[:, 1].min(),
            'y_max': points[:, 1].max(),
            'z_min': points[:, 2].min(),
            'z_max': points[:, 2].max(),
        },
        'center': points.mean(axis=0),
        'std': points.std(axis=0),
        'pcd': pcd
    }
    
    return stats

def compare_pointclouds(original_ply="PointCloud.ply", generated_ply="anh1_from_dat.ply"):
    """Compare two point clouds and create visualization"""
    
    print("🔍 Loading and analyzing point clouds...")
    
    # Load original point cloud
    original_stats = load_and_analyze_pointcloud(original_ply)
    if original_stats is None:
        return
    
    # Load generated point cloud
    generated_stats = load_and_analyze_pointcloud(generated_ply)
    if generated_stats is None:
        return
    
    # Print comparison
    print("\n📊 Point Cloud Comparison:")
    print("=" * 60)
    print(f"{'Metric':<20} {'Original':<20} {'Generated':<20}")
    print("=" * 60)
    print(f"{'File':<20} {os.path.basename(original_stats['file']):<20} {os.path.basename(generated_stats['file']):<20}")
    print(f"{'Points':<20} {original_stats['num_points']:,<20} {generated_stats['num_points']:,<20}")
    
    print(f"\n{'Bounds (X)':<20} {original_stats['bounds']['x_min']:.2f} to {original_stats['bounds']['x_max']:.2f}    {generated_stats['bounds']['x_min']:.2f} to {generated_stats['bounds']['x_max']:.2f}")
    print(f"{'Bounds (Y)':<20} {original_stats['bounds']['y_min']:.2f} to {original_stats['bounds']['y_max']:.2f}    {generated_stats['bounds']['y_min']:.2f} to {generated_stats['bounds']['y_max']:.2f}")
    print(f"{'Bounds (Z)':<20} {original_stats['bounds']['z_min']:.2f} to {original_stats['bounds']['z_max']:.2f}    {generated_stats['bounds']['z_min']:.2f} to {generated_stats['bounds']['z_max']:.2f}")
    
    print(f"\n{'Center (X,Y,Z)':<20} ({original_stats['center'][0]:.2f}, {original_stats['center'][1]:.2f}, {original_stats['center'][2]:.2f})")
    print(f"{'                ':<20} ({generated_stats['center'][0]:.2f}, {generated_stats['center'][1]:.2f}, {generated_stats['center'][2]:.2f})")
    
    # Create side-by-side visualization
    print("\n🎨 Creating comparison visualization...")
    
    fig = plt.figure(figsize=(20, 10))
    
    # Original point cloud visualization
    plt.subplot(2, 4, 1)
    original_points = np.asarray(original_stats['pcd'].points)
    plt.scatter(original_points[:, 0], original_points[:, 2], s=0.1, alpha=0.6, c='blue')
    plt.title(f'Original PLY - Top View\n{original_stats["num_points"]:,} points')
    plt.xlabel('X (mm)')
    plt.ylabel('Z (mm)')
    plt.axis('equal')
    
    # Generated point cloud visualization
    plt.subplot(2, 4, 2)
    generated_points = np.asarray(generated_stats['pcd'].points)
    plt.scatter(generated_points[:, 0], generated_points[:, 2], s=0.1, alpha=0.6, c='red')
    plt.title(f'Generated PLY - Top View\n{generated_stats["num_points"]:,} points')
    plt.xlabel('X (mm)')
    plt.ylabel('Z (mm)')
    plt.axis('equal')
    
    # Side view comparisons
    plt.subplot(2, 4, 3)
    plt.scatter(original_points[:, 0], original_points[:, 1], s=0.1, alpha=0.6, c='blue')
    plt.title('Original PLY - Side View')
    plt.xlabel('X (mm)')
    plt.ylabel('Y (mm)')
    plt.axis('equal')
    
    plt.subplot(2, 4, 4)
    plt.scatter(generated_points[:, 0], generated_points[:, 1], s=0.1, alpha=0.6, c='red')
    plt.title('Generated PLY - Side View')
    plt.xlabel('X (mm)')
    plt.ylabel('Y (mm)')
    plt.axis('equal')
    
    # Histograms
    plt.subplot(2, 4, 5)
    plt.hist(original_points[:, 2], bins=50, alpha=0.7, label='Original', color='blue')
    plt.hist(generated_points[:, 2], bins=50, alpha=0.7, label='Generated', color='red')
    plt.xlabel('Z coordinate (mm)')
    plt.ylabel('Frequency')
    plt.title('Z Distribution Comparison')
    plt.legend()
    
    plt.subplot(2, 4, 6)
    plt.hist(original_points[:, 0], bins=50, alpha=0.7, label='Original', color='blue')
    plt.hist(generated_points[:, 0], bins=50, alpha=0.7, label='Generated', color='red')
    plt.xlabel('X coordinate (mm)')
    plt.ylabel('Frequency')
    plt.title('X Distribution Comparison')
    plt.legend()
    
    # Overlay comparison
    plt.subplot(2, 4, 7)
    plt.scatter(original_points[::10, 0], original_points[::10, 2], s=0.5, alpha=0.5, c='blue', label='Original')
    plt.scatter(generated_points[::10, 0], generated_points[::10, 2], s=0.5, alpha=0.5, c='red', label='Generated')
    plt.title('Overlay Comparison - Top View')
    plt.xlabel('X (mm)')
    plt.ylabel('Z (mm)')
    plt.legend()
    plt.axis('equal')
    
    # Statistics summary
    plt.subplot(2, 4, 8)
    plt.axis('off')
    stats_text = f"""
Point Cloud Statistics:

Original:
• Points: {original_stats['num_points']:,}
• X range: {original_stats['bounds']['x_max'] - original_stats['bounds']['x_min']:.1f} mm
• Y range: {original_stats['bounds']['y_max'] - original_stats['bounds']['y_min']:.1f} mm  
• Z range: {original_stats['bounds']['z_max'] - original_stats['bounds']['z_min']:.1f} mm

Generated:
• Points: {generated_stats['num_points']:,}
• X range: {generated_stats['bounds']['x_max'] - generated_stats['bounds']['x_min']:.1f} mm
• Y range: {generated_stats['bounds']['y_max'] - generated_stats['bounds']['y_min']:.1f} mm
• Z range: {generated_stats['bounds']['z_max'] - generated_stats['bounds']['z_min']:.1f} mm

Difference:
• Points: {generated_stats['num_points'] - original_stats['num_points']:+,}
• Ratio: {generated_stats['num_points'] / original_stats['num_points']:.2f}x
    """
    plt.text(0.05, 0.95, stats_text, transform=plt.gca().transAxes, 
             verticalalignment='top', fontfamily='monospace', fontsize=9)
    
    plt.tight_layout()
    plt.savefig('pointcloud_comparison.png', dpi=300, bbox_inches='tight')
    print("💾 Comparison saved as: pointcloud_comparison.png")
    
    plt.show()
    
    # Display 3D comparison
    print("\n🔵 Displaying 3D comparison...")
    print("   Blue = Original, Red = Generated")
    
    # Color the point clouds
    original_stats['pcd'].paint_uniform_color([0, 0, 1])  # Blue
    generated_stats['pcd'].paint_uniform_color([1, 0, 0])  # Red
    
    # Display both
    o3d.visualization.draw_geometries([original_stats['pcd'], generated_stats['pcd']])

def main():
    import sys
    
    original_ply = "PointCloud.ply"
    generated_ply = "anh1_from_dat.ply"
    
    if len(sys.argv) > 1:
        original_ply = sys.argv[1]
    if len(sys.argv) > 2:
        generated_ply = sys.argv[2]
    
    print("🔧 Point Cloud Comparison Tool")
    print("=" * 40)
    print(f"Original: {original_ply}")
    print(f"Generated: {generated_ply}")
    print("=" * 40)
    
    compare_pointclouds(original_ply, generated_ply)

if __name__ == "__main__":
    main()
