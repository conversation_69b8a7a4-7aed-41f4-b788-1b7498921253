#!/usr/bin/env python3
"""
Simple script to process SICK 3D data files (.dat + .xml) and create beautiful 2D images
Usage: python process_sick_data.py <dat_file> <xml_file>
"""

import sys
import os
from visualize_3d_data import visualize_3d_data, create_point_cloud_from_sick_data

def main():
    if len(sys.argv) != 3:
        print("Usage: python process_sick_data.py <dat_file> <xml_file>")
        print("\nExample:")
        print("python process_sick_data.py anhtraning/anh1/anh1.dat anhtraning/anh1/anh1.xml")
        return
    
    dat_file = sys.argv[1]
    xml_file = sys.argv[2]
    
    # Check if files exist
    if not os.path.exists(dat_file):
        print(f"Error: DAT file not found: {dat_file}")
        return
    
    if not os.path.exists(xml_file):
        print(f"Error: XML file not found: {xml_file}")
        return
    
    print(f"Processing SICK 3D data:")
    print(f"  DAT file: {dat_file}")
    print(f"  XML file: {xml_file}")
    print()
    
    try:
        # Process and visualize data
        print("Creating 2D visualizations...")
        range_data, intensity_data, X, Y, Z, valid_mask = visualize_3d_data(dat_file, xml_file)
        
        # Create point cloud
        print("Creating 3D point cloud...")
        pcd = create_point_cloud_from_sick_data(dat_file, xml_file)
        
        base_name = os.path.splitext(os.path.basename(dat_file))[0]
        
        print(f"\n✅ Processing completed successfully!")
        print(f"📁 Generated files:")
        print(f"   🖼️  {base_name}_complete_analysis.png - Complete analysis with 6 views")
        print(f"   🎨 {base_name}_enhanced_2d.png - Enhanced 2D visualization")
        print(f"   📊 {base_name}_range_hq.png - High-quality depth map")
        print(f"   🔵 {base_name}_from_dat.ply - 3D point cloud")
        
        print(f"\n📈 Data statistics:")
        print(f"   Valid 3D points: {valid_mask.sum():,}")
        print(f"   Image dimensions: {range_data.shape}")
        print(f"   Range values: {range_data.min():.0f} - {range_data.max():.0f}")
        print(f"   Intensity values: {intensity_data.min():.0f} - {intensity_data.max():.0f}")
        
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
