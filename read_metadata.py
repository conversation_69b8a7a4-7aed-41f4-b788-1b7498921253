import open3d as o3d
import numpy as np

# <PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiế<PERSON> từ file .ply
file_path = r"D:\SICK\3DSick\PointCloud.ply"
pcd = o3d.io.read_point_cloud(file_path)

# Nếu muốn downsample
voxel_size = 0.03
pcd = pcd.voxel_down_sample(voxel_size=voxel_size)

# Nếu muốn lọc nhiễu
nb_neighbors = 20
std_ratio = 1.0
pcd, ind = pcd.remove_statistical_outlier(nb_neighbors=nb_neighbors, std_ratio=std_ratio)

# Hi<PERSON>n thị
o3d.visualization.draw_geometries([pcd])

