import numpy as np  # Th<PERSON> viện numpy

dat_file = r"D:\SICK\3DSick\anhtraning\anh1\anh1.dat"
depth_raw = np.fromfile(dat_file, dtype=np.uint16)

print("Số phần tử trong file:", depth_raw.size)
print("Chia hết cho width 2560? ", depth_raw.size % 2560 == 0)
print("Chia hết cho height 832? ", depth_raw.size % 832 == 0)

expected_size = 832 * 2560
print("Expected size:", expected_size)
print("Thừa thiếu:", depth_raw.size - expected_size)