# SICK 3D Data Visualization Tool

Công cụ chuyên dụng để đọc và hiển thị dữ liệu 3D từ camera SICK Ruler3004, tạo ra những hình ảnh 2D chân thật và đẹp mắt.

## 🎯 Tính năng chính

- ✅ Đọc dữ liệu từ file `.dat` và `.xml` của SICK camera
- ✅ Tạo hình ảnh 2D chất lượng cao với nhiều góc nhìn
- ✅ Chuyển đổi sang point cloud 3D (.ply)
- ✅ So sánh với dữ liệu gốc
- ✅ Xử lý hàng loạt nhiều file

## 📁 Cấu trúc file đã tạo

```
📦 Generated Files
├── 🖼️ anh1_complete_analysis.png     # Phân tích tổng thể 6 góc nhìn
├── 🎨 anh1_enhanced_2d.png           # Hình ảnh 2D được cải thiện
├── 📊 anh1_range_hq.png              # Depth map chất lượng cao
├── 🔵 anh1_from_dat.ply              # Point cloud 3D từ .dat
├── 📈 pointcloud_comparison.png      # So sánh với file gốc
└── 🔧 Scripts
    ├── visualize_3d_data.py          # Script chính
    ├── process_sick_data.py          # Xử lý 1 file
    ├── batch_process_sick_data.py    # Xử lý hàng loạt
    └── compare_pointclouds.py        # So sánh point clouds
```

## 🚀 Cách sử dụng

### 1. Xử lý một file đơn lẻ
```bash
python process_sick_data.py anhtraning/anh1/anh1.dat anhtraning/anh1/anh1.xml
```

### 2. Xử lý hàng loạt tất cả file trong thư mục
```bash
python batch_process_sick_data.py anhtraning
```

### 3. So sánh với file PointCloud.ply gốc
```bash
python compare_pointclouds.py PointCloud.ply anh1_from_dat.ply
```

### 4. Chạy script chính với nhiều tùy chọn
```bash
python visualize_3d_data.py
```

## 📊 Kết quả đã đạt được

### Dữ liệu từ anh1.dat:
- **855,571 điểm 3D hợp lệ** từ camera SICK Ruler3004
- **Kích thước**: 500 x 3200 pixels (line scan data)
- **Range values**: 0 - 65,445 (thông tin độ sâu)
- **Intensity values**: 0 - 250 (cường độ phản xạ)

### Chất lượng hình ảnh:
- **DPI**: 300 (chất lượng in ấn)
- **Định dạng**: PNG với nén không mất dữ liệu
- **Màu sắc**: Sử dụng colormap chuyên nghiệp (jet, viridis, terrain)
- **Tỷ lệ**: Giữ nguyên tỷ lệ thực của dữ liệu 3D

## 🎨 Các loại hình ảnh được tạo

### 1. Complete Analysis (6 góc nhìn)
- Range Image (Depth Map)
- Intensity Image  
- Height Map (World Z coordinates)
- 3D Surface Plot với màu intensity
- Cross-section view
- Enhanced 2D View (RGB combination)

### 2. Enhanced 2D Image
- Kết hợp Range và Intensity
- Màu sắc được tối ưu hóa
- Độ tương phản cao

### 3. High-Quality Range Image
- Depth map với colormap jet
- Hiển thị chi tiết độ sâu
- Colorbar với đơn vị đo

## 🔧 Yêu cầu hệ thống

```python
# Các thư viện cần thiết
numpy
matplotlib
opencv-python
open3d
```

## 📈 Thông số kỹ thuật

### Camera SICK Ruler3004:
- **Model**: V3DX3-004BR21A
- **Firmware**: FW:4.2.0.20192, FPGA:5.1.2263
- **Scan Type**: Linescan3D
- **Resolution**: 3200 x 500 pixels
- **Range Resolution**: SixteenthPixel
- **Extraction Method**: Hi3D

### Calibration Parameters:
- **X Scale**: 0.0159 mm/pixel
- **X Offset**: 24.03 mm
- **Z Scale**: 0.000343 mm/unit
- **Z Offset**: 25.03 mm

## 🎯 Ưu điểm so với file .ply gốc

1. **Dữ liệu đầy đủ**: Giữ nguyên thông tin intensity
2. **Chất lượng cao**: Không bị downsample
3. **Màu sắc chân thật**: Sử dụng intensity thực tế
4. **Nhiều góc nhìn**: 6 cách hiển thị khác nhau
5. **Thông tin chi tiết**: Metadata và statistics đầy đủ

## 🔍 Troubleshooting

### Lỗi thường gặp:
1. **"No valid points found"**: Kiểm tra cấu trúc file .dat
2. **"File not found"**: Đảm bảo đường dẫn file đúng
3. **"Memory error"**: Giảm kích thước dữ liệu hoặc tăng RAM

### Tips:
- Sử dụng `batch_process_sick_data.py` cho nhiều file
- Kiểm tra file .xml để hiểu cấu trúc dữ liệu
- Sử dụng `compare_pointclouds.py` để verify kết quả

## 📞 Hỗ trợ

Script được tối ưu hóa cho dữ liệu từ camera SICK Ruler3004. 
Có thể điều chỉnh cho các model camera SICK khác bằng cách sửa parameters trong XML.

---
*Tạo bởi AI Assistant - Tối ưu hóa cho dữ liệu SICK 3D*
